{"name": "knep-demo-client", "version": "1.0.0", "description": "React + Material-UI application with Okta OIDC authentication for Kong Native Event Proxy", "main": "server.js", "license": "Apache-2.0", "scripts": {"start": "concurrently \"npm run server\" \"npm run client\"", "server": "node server.js", "client": "cd client && npm start", "build": "cd client && npm run build", "dev": "npm start", "install-client": "cd client && npm install", "type-check": "cd client && npm run type-check", "type-check:watch": "cd client && npm run type-check:watch"}, "dependencies": {"@okta/oidc-middleware": "^5.5.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.18.2", "kafkajs": "^2.2.4", "path": "^0.12.7"}, "devDependencies": {"concurrently": "^8.2.2"}}