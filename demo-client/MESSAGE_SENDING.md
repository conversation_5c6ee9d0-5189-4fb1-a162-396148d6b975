# Message Sending Feature

This document describes the message sending functionality added to the KNEP Demo Client.

## Overview

The application now supports sending messages to Kafka topics in addition to querying them. This feature allows users to produce messages to any topic they have access to through the KNEP virtual clusters.

## Features

### Send Message Form
- **Location**: Available on the Topic Messages page (`/kafka/topics/:topicName`)
- **UI**: Collapsible accordion form at the top of the messages list
- **Fields**:
  - **Message Key** (optional): Used for partitioning and message ordering
  - **Message Value** (required): The actual message content
  - **Partition** (optional): Specific partition to send to (auto-selected if not specified)
  - **Headers** (optional): Custom key-value pairs for message metadata

### Advanced Features
- **JSON Formatting**: Format JSON button to automatically format JSON content
- **Dynamic Headers**: Add/remove custom headers as needed
- **Partition Selection**: Choose specific partition or let Kafka auto-assign
- **Real-time Feedback**: Success/error messages with detailed information
- **Form Reset**: Automatic form reset after successful message sending
- **Message Refresh**: Automatically refreshes message list after sending

## API Endpoints

### Standard Configuration
- `POST /api/kafka/topics/:topicName/produce`
  - Sends a message to the specified topic using default Kafka configuration
  - Request body: `KafkaProduceMessageRequest`
  - Response: `KafkaProduceMessageResponse`

### Custom Configuration
- `POST /api/kafka/topics-with-config/:topicName/produce`
  - Sends a message using custom Kafka connection configuration
  - Request body: `{ message: KafkaProduceMessageRequest, config: KafkaConnectionConfig }`
  - Response: `KafkaProduceMessageResponse`

## Data Types

### KafkaProduceMessageRequest
```typescript
interface KafkaProduceMessageRequest {
  key?: string;           // Optional message key
  value: string;          // Required message content
  headers?: Record<string, string>; // Optional headers
  partition?: number;     // Optional partition number
}
```

### KafkaProduceMessageResponse
```typescript
interface KafkaProduceMessageResponse {
  success: boolean;       // Operation success status
  message: string;        // Success/error message
  topic: string;          // Topic name
  partition: number;      // Partition where message was sent
  offset: string;         // Message offset in partition
  timestamp: string;      // ISO timestamp
}
```

## Usage

1. **Navigate** to a topic's messages page
2. **Expand** the "Send Message to Topic" accordion
3. **Fill in** the message value (required)
4. **Optionally** add:
   - Message key for partitioning
   - Custom headers for metadata
   - Specific partition number
5. **Click** "Send Message" to produce the message
6. **View** success confirmation with partition and offset information
7. **See** the new message appear in the messages list

## Configuration Support

The message sending feature supports both:
- **Default Configuration**: Uses the standard Kafka connection settings
- **Custom Configuration**: Uses user-defined connection settings when available

The system automatically detects which configuration to use based on the current context.

## Error Handling

- **Validation**: Ensures message value is provided
- **Network Errors**: Displays connection and timeout errors
- **Kafka Errors**: Shows Kafka-specific errors (authentication, topic access, etc.)
- **User Feedback**: Clear error messages with retry options

## Testing

The feature includes:
- **Unit Tests**: API method validation and type checking
- **Integration**: End-to-end message sending flow
- **Error Scenarios**: Proper error handling and user feedback

## Security

- **Authentication**: Requires valid Okta access token
- **Authorization**: Respects Kafka topic permissions
- **Input Validation**: Sanitizes and validates all input data
- **Error Sanitization**: Prevents sensitive information leakage in error messages
