# Documentation Overview

This directory contains comprehensive documentation for the Kong Event Gateway demo.

## Quick Start

1. **[Environment Variables Reference](environment-variables.md)** - Quick reference for all environment variables
2. **[Configuration Guide](configuration.md)** - System configuration and architecture details

## Documentation Structure

### Core Documentation

| Document | Purpose | Audience |
|----------|---------|----------|
| **[Environment Variables](environment-variables.md)** | Quick reference for all environment variables | Developers, DevOps |
| **[Configuration Guide](configuration.md)** | System architecture and configuration details | Architects, Operators |
| **[Setup Guide](setup.md)** | Step-by-step setup instructions | All users |
| **[Troubleshooting](troubleshooting.md)** | Common issues and solutions | Support, Developers |

### Specialized Guides

| Document | Purpose | Audience |
|----------|---------|----------|
| **[Deployment Guide](deployment.md)** | Production deployment patterns | DevOps, SRE |
| **[Security Guide](security.md)** | Security configuration and best practices | Security teams |
| **[API Reference](api.md)** | Demo client API documentation | Developers |

## Documentation Philosophy

### Clear Separation of Concerns

- **Environment Variables**: Focused reference for configuration values
- **Configuration Guide**: System architecture and component configuration
- **Setup Guide**: Step-by-step instructions for getting started
- **Troubleshooting**: Problem-solving and diagnostics

### Minimal Duplication

- Each document has a specific purpose and audience
- Cross-references used instead of duplicating content
- Quick reference tables for easy lookup
- Detailed explanations where needed

### User-Centric Organization

- **Quick Start**: Get running fast with minimal reading
- **Reference**: Detailed information when needed
- **Troubleshooting**: Problem-solving focus
- **Advanced**: Production and customization topics

## Getting Help

1. **Quick Issues**: Check [Environment Variables](environment-variables.md) reference
2. **Configuration Problems**: See [Configuration Guide](configuration.md)
3. **Setup Issues**: Follow [Setup Guide](setup.md) step-by-step
4. **Runtime Problems**: Use [Troubleshooting Guide](troubleshooting.md)

## Contributing to Documentation

When updating documentation:

1. **Maintain Separation**: Keep each document focused on its purpose
2. **Avoid Duplication**: Use cross-references instead of copying content
3. **Update Cross-References**: Ensure links remain valid
4. **Test Instructions**: Verify all commands and examples work
5. **Consider Audience**: Write for the intended reader level

## Documentation Standards

### Format Guidelines

- **Headers**: Use clear, descriptive headers
- **Tables**: Use for structured reference information
- **Code Blocks**: Include language hints and comments
- **Cross-References**: Link to related sections
- **Examples**: Provide complete, working examples

### Content Guidelines

- **Concise**: Get to the point quickly
- **Complete**: Include all necessary information
- **Current**: Keep information up-to-date
- **Tested**: Verify all examples and commands work
- **Accessible**: Write for the intended audience level
