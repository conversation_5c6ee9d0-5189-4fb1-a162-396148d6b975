name: kafka_cluster_with_knep

services:
  # Kong Event Gateway
  kong-event-gateway:
    image: kong/kong-native-event-proxy:latest
    container_name: knep-konnect
    ports:
      - "19092-19192:19092-19192"
      - "29092-29192:29092-29192"
      - "8080:8080"
      - "8443:8443"
    environment:
      KONNECT_API_TOKEN: ${KONNECT_API_TOKEN:-}
      KONNECT_API_HOSTNAME: ${KONNECT_API_HOSTNAME:-us.api.konghq.com}
      KONNECT_CONTROL_PLANE_ID: ${KONNECT_CONTROL_PLANE_ID:-}
      KNEP__RUNTIME__DRAIN_DURATION: 1s # makes shutdown quicker, not recommended to be set like this in production
      # KNEP__OBSERVABILITY__LOG_FLAGS: "info,knep=debug" # Uncomment for debug logging
    healthcheck:
      test: curl -f http://localhost:8080/health/probes/liveness
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - ./config/certs:/var/tls
    networks:
      - kong-kafka-net
    secrets:
      - confluent_cloud_username
      - confluent_cloud_password

  # Demo Client Application (React + Material-UI + Node.js API)
  demo-client:
    build:
      context: ./demo-client
      dockerfile: Dockerfile
      args:
        REACT_APP_OKTA_ISSUER: ${OKTA_ISSUER}
        REACT_APP_OKTA_CLIENT_ID: ${OKTA_CLIENT_ID}
        REACT_APP_API_URL: ""
    container_name: demo-client
    depends_on:
      - kong-event-gateway
    ports:
      - "3000:3001"  # Map host port 3000 to container port 3001 (production server)
    environment:
      # Node.js API Server Runtime Configuration
      NODE_ENV: production
      PORT: 3001
      # Okta Configuration (for API server)
      OKTA_CLIENT_ID: ${OKTA_CLIENT_ID}
      OKTA_ISSUER: ${OKTA_ISSUER}
      # Kafka Configuration (for API server)
      KAFKA_BOOTSTRAP: ${KAFKA_BOOTSTRAP:-localhost:19092}
      # Note: React environment variables are baked into the build during Docker build phase
    networks:
      - kong-kafka-net
    profiles:
      - demo

networks:
  kong-kafka-net:
    driver: bridge

secrets:
  confluent_cloud_username:
    file: ./config/secrets/confluent-api-key.txt
  confluent_cloud_password:
    file: ./config/secrets/confluent-api-secret.txt
