# Environment variables
.env
*.env
!*.env.example

# Docker
.docker/
docker-compose.override.yml

# Logs
logs/
*.log

# Temporary files
tmp/
temp/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Certificates and keys
*.pem
*.key
*.crt
*.p12
*.jks

# Backup files
*.bak
*.backup

# Node.js (if using Node.js examples)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python (if using Python examples)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Java (if using Java examples)
*.class
*.jar
*.war
*.ear
target/
.gradle/
build/

# Go (if using Go examples)
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
vendor/

# Kafka data (if running locally)
kafka-logs/
zookeeper-data/

# Kong data
kong-data/

# Secrets
config/secrets/*
!config/secrets/README.md
