backend_clusters:
  - name: confluent-cloud
    bootstrap_servers: 
      - <replace-with-your-bootstrap-server>:<replace-with-your-port>
    authentication:
      type: sasl_plain
      sasl_plain:
        username:
          type: file
          file:
            path: /run/secrets/confluent_cloud_username
        password:
          type: file
          file:
            path: /run/secrets/confluent_cloud_password
    tls:
      insecure_skip_verify: true
listeners:
  port:
    - advertised_host: knep-konnect
      listen_address: 0.0.0.0
      listen_port_start: 19092
virtual_clusters:
  - authentication:
      - type: sasl_oauth_bearer
        sasl_oauth_bearer:
          jwks:
            endpoint: https://your-domain.okta.com/oauth2/v1/keys
            timeout: 1s
        mediation:
          type: use_backend_cluster
    backend_cluster_name: confluent-cloud
    name: team-c
    route_by:
      type: port
      port:
        min_broker_id: 0
