backend_clusters:
  - bootstrap_servers:
      - kafka1:9092
      - kafka2:9092
      - kafka3:9092
    name: kafka-localhost
listeners:
  port:
    - advertised_host: knep-konnect
      listen_address: 0.0.0.0
      listen_port_start: 19092
virtual_clusters:
  - authentication:
      - mediation:
          type: anonymous
        sasl_oauth_bearer:
          jwks:
            endpoint: https://your-domain.okta.com/oauth2/v1/keys
            timeout: 1s
        type: sasl_oauth_bearer
    backend_cluster_name: kafka-localhost
    name: team-a
    rewrite_ids:
      type: prefix
    route_by:
      port:
        min_broker_id: 1
      type: port
    topic_rewrite:
      prefix:
        value: a-
      type: prefix
  - authentication:
      - mediation:
          type: anonymous
        type: anonymous
    backend_cluster_name: kafka-localhost
    name: team-b
    rewrite_ids:
      type: prefix
    route_by:
      port:
        min_broker_id: 1
        offset: 10000
      type: port
    topic_rewrite:
      prefix:
        value: b-
      type: prefix
