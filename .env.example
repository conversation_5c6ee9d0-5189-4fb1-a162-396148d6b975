# Environment Variables for Kong Native Event Proxy + <PERSON><PERSON> + Ka<PERSON>ka Demo
# Copy this file to .env and update with your actual values

# Okta Configuration (used by both demo client and KNEP)
OKTA_CLIENT_ID=your_okta_client_id_here
OKTA_ISSUER=https://your-domain.okta.com

# Demo Client Configuration
CALLBACK_URL=http://localhost:3000/auth/callback
SESSION_SECRET=your-session-secret-change-in-production

# Kafka Configuration (for demo client API)
KAFKA_BOOTSTRAP=localhost:19092

# Kong Konnect Configuration (for KNEP)
KONNECT_API_TOKEN=your_konnect_token
KONNECT_API_HOSTNAME=us.api.konghq.com
KONNECT_CONTROL_PLANE_ID=your_control_plane_id

# React App Build-time Variables (for Docker builds)
# These are the same as <PERSON><PERSON> config above, used during React build
REACT_APP_OKTA_ISSUER=${OKTA_ISSUER}
REACT_APP_OKTA_CLIENT_ID=${OKTA_CLIENT_ID}
REACT_APP_API_URL=""